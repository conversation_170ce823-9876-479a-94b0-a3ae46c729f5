{"validation_timestamp": "2025-07-29T11:45:18.297534", "validation_type": "historical_momentum_balance_ratios", "configuration": {"n_splits": 5, "test_size": 0.2, "bootstrap_samples": 1000000, "temporal_validation": true, "min_context_sample_size": 150, "significance_threshold": 0.05, "balance_ratios_tested": [[0.1, 0.9], [0.15, 0.85], [0.2, 0.8], [0.25, 0.75], [0.3, 0.7], [0.35, 0.65], [0.4, 0.6], [0.45, 0.55], [0.5, 0.5], [0.55, 0.44999999999999996], [0.6, 0.4], [0.65, 0.35], [0.7, 0.30000000000000004], [0.75, 0.25], [0.8, 0.19999999999999996], [0.85, 0.15000000000000002], [0.9, 0.09999999999999998]]}, "context_results": {"Clay_Set1_Mid": {"optimal_balance": {"historical_ratio": 0.24674783189293106, "momentum_ratio": 0.753252168107069, "accuracy": 0.7044374528301885, "confidence_interval_95": [0.660377358490566, 0.7358490566037735], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 159}, "cross_validation_info": {"configured_folds": 5, "valid_folds": 3, "skipped_folds": 2, "fold_usage_rate": 0.6}, "all_ratios_tested": {"0.737_0.263": {"average_accuracy": 0.6226415094339622, "std_accuracy": 0.0815187509233693, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.247_0.753": {"average_accuracy": 0.7044025157232704, "std_accuracy": 0.032069305116935745, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.724_0.276": {"average_accuracy": 0.5849056603773585, "std_accuracy": 0.0770279793328043, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.577_0.423": {"average_accuracy": 0.6540880503144654, "std_accuracy": 0.023532436394804658, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.457_0.543": {"average_accuracy": 0.6352201257861635, "std_accuracy": 0.032069305116935745, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.180_0.820": {"average_accuracy": 0.6981132075471698, "std_accuracy": 0.03081119173312173, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.467_0.533": {"average_accuracy": 0.6163522012578616, "std_accuracy": 0.08484740605806314, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.367_0.633": {"average_accuracy": 0.6352201257861635, "std_accuracy": 0.017788849841171003, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.214_0.786": {"average_accuracy": 0.6352201257861636, "std_accuracy": 0.11562752396761154, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.621_0.379": {"average_accuracy": 0.610062893081761, "std_accuracy": 0.017788849841171003, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.100_0.900": {"average_accuracy": 0.5974842767295597, "std_accuracy": 0.05832464462575913, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.255_0.745": {"average_accuracy": 0.610062893081761, "std_accuracy": 0.08484740605806311, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.900_0.100": {"average_accuracy": 0.5723270440251572, "std_accuracy": 0.0495220621007032, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.243_0.757": {"average_accuracy": 0.5786163522012578, "std_accuracy": 0.017788849841171003, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.184_0.816": {"average_accuracy": 0.6226415094339622, "std_accuracy": 0.04075937546168465, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.173_0.827": {"average_accuracy": 0.610062893081761, "std_accuracy": 0.032069305116935745, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.196_0.804": {"average_accuracy": 0.6540880503144654, "std_accuracy": 0.06413861023387149, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.533_0.467": {"average_accuracy": 0.6352201257861635, "std_accuracy": 0.04447212460292751, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.314_0.686": {"average_accuracy": 0.6666666666666666, "std_accuracy": 0.05410267463548821, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.327_0.673": {"average_accuracy": 0.6540880503144654, "std_accuracy": 0.023532436394804658, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.419_0.581": {"average_accuracy": 0.5786163522012578, "std_accuracy": 0.0875999262715982, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.298_0.702": {"average_accuracy": 0.6729559748427673, "std_accuracy": 0.038769899389741985, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.807_0.193": {"average_accuracy": 0.6226415094339622, "std_accuracy": 0.03081119173312173, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.299_0.701": {"average_accuracy": 0.6477987421383647, "std_accuracy": 0.03557769968234201, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.343_0.657": {"average_accuracy": 0.660377358490566, "std_accuracy": 0.04621678759968259, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.663_0.337": {"average_accuracy": 0.5974842767295597, "std_accuracy": 0.03557769968234201, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.549_0.451": {"average_accuracy": 0.5345911949685535, "std_accuracy": 0.054102674635488235, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.355_0.645": {"average_accuracy": 0.5660377358490566, "std_accuracy": 0.04621678759968259, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.808_0.192": {"average_accuracy": 0.6037735849056604, "std_accuracy": 0.05336654952351301, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.272_0.728": {"average_accuracy": 0.5471698113207547, "std_accuracy": 0.04075937546168465, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.448_0.552": {"average_accuracy": 0.610062893081761, "std_accuracy": 0.03557769968234201, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.655_0.345": {"average_accuracy": 0.6037735849056604, "std_accuracy": 0.06162238346624346, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.307_0.693": {"average_accuracy": 0.6415094339622641, "std_accuracy": 0.05336654952351301, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.335_0.665": {"average_accuracy": 0.6037735849056604, "std_accuracy": 0.04621678759968259, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.320_0.680": {"average_accuracy": 0.6352201257861635, "std_accuracy": 0.044472124602927515, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.290_0.710": {"average_accuracy": 0.5660377358490566, "std_accuracy": 0.04621678759968259, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.588_0.412": {"average_accuracy": 0.6415094339622641, "std_accuracy": 0.07059730918441397, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.204_0.796": {"average_accuracy": 0.5974842767295597, "std_accuracy": 0.044472124602927515, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.599_0.401": {"average_accuracy": 0.5911949685534591, "std_accuracy": 0.06226097444409851, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.569_0.431": {"average_accuracy": 0.6540880503144654, "std_accuracy": 0.044472124602927515, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.502_0.498": {"average_accuracy": 0.6289308176100629, "std_accuracy": 0.06413861023387149, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.519_0.481": {"average_accuracy": 0.5471698113207547, "std_accuracy": 0.07059730918441395, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.381_0.619": {"average_accuracy": 0.5786163522012578, "std_accuracy": 0.032069305116935745, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.487_0.513": {"average_accuracy": 0.6163522012578616, "std_accuracy": 0.06226097444409851, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.755_0.245": {"average_accuracy": 0.5723270440251572, "std_accuracy": 0.038769899389741985, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.848_0.152": {"average_accuracy": 0.6289308176100629, "std_accuracy": 0.023532436394804658, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.863_0.137": {"average_accuracy": 0.610062893081761, "std_accuracy": 0.08484740605806314, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.833_0.167": {"average_accuracy": 0.6226415094339622, "std_accuracy": 0.04075937546168465, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.142_0.858": {"average_accuracy": 0.6289308176100629, "std_accuracy": 0.023532436394804658, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.127_0.873": {"average_accuracy": 0.5534591194968553, "std_accuracy": 0.032069305116935745, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.155_0.845": {"average_accuracy": 0.5786163522012578, "std_accuracy": 0.06946767935337896, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.788_0.212": {"average_accuracy": 0.5911949685534591, "std_accuracy": 0.0495220621007032, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.692_0.308": {"average_accuracy": 0.660377358490566, "std_accuracy": 0.06715143554736672, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.701_0.299": {"average_accuracy": 0.5408805031446541, "std_accuracy": 0.008894424920585502, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.682_0.318": {"average_accuracy": 0.6037735849056604, "std_accuracy": 0.04075937546168465, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.226_0.774": {"average_accuracy": 0.5911949685534591, "std_accuracy": 0.07905537792438071, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.637_0.363": {"average_accuracy": 0.6729559748427673, "std_accuracy": 0.017788849841171003, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.643_0.357": {"average_accuracy": 0.5911949685534591, "std_accuracy": 0.03557769968234201, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.631_0.369": {"average_accuracy": 0.6037735849056604, "std_accuracy": 0.05336654952351301, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.310_0.690": {"average_accuracy": 0.610062893081761, "std_accuracy": 0.11562752396761157, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.189_0.811": {"average_accuracy": 0.6226415094339622, "std_accuracy": 0.03081119173312173, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.581_0.419": {"average_accuracy": 0.5849056603773585, "std_accuracy": 0.09243357519936521, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.822_0.178": {"average_accuracy": 0.559748427672956, "std_accuracy": 0.054102674635488186, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.421_0.579": {"average_accuracy": 0.6037735849056604, "std_accuracy": 0.06715143554736672, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.400_0.600": {"average_accuracy": 0.5849056603773585, "std_accuracy": 0.09620791535080722, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.877_0.123": {"average_accuracy": 0.6477987421383647, "std_accuracy": 0.03557769968234201, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.564_0.436": {"average_accuracy": 0.5471698113207547, "std_accuracy": 0.061622383466243484, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.436_0.564": {"average_accuracy": 0.5849056603773585, "std_accuracy": 0.026683274761756505, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.772_0.228": {"average_accuracy": 0.6415094339622641, "std_accuracy": 0.13341637380878263, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.477_0.523": {"average_accuracy": 0.610062893081761, "std_accuracy": 0.07280400567792593, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.332_0.668": {"average_accuracy": 0.6226415094339622, "std_accuracy": 0.015405595866560866, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.883_0.117": {"average_accuracy": 0.6163522012578616, "std_accuracy": 0.08484740605806314, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.179_0.821": {"average_accuracy": 0.660377358490566, "std_accuracy": 0.04621678759968259, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.871_0.129": {"average_accuracy": 0.5660377358490566, "std_accuracy": 0.04621678759968259, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.611_0.389": {"average_accuracy": 0.6226415094339622, "std_accuracy": 0.03081119173312173, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.322_0.678": {"average_accuracy": 0.5849056603773585, "std_accuracy": 0.06715143554736669, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.167_0.833": {"average_accuracy": 0.559748427672956, "std_accuracy": 0.08759992627159824, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.303_0.697": {"average_accuracy": 0.5723270440251572, "std_accuracy": 0.07753979877948398, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.432_0.568": {"average_accuracy": 0.6037735849056604, "std_accuracy": 0.05554566582596129, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.841_0.159": {"average_accuracy": 0.5345911949685535, "std_accuracy": 0.047064872789609344, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.495_0.505": {"average_accuracy": 0.5534591194968553, "std_accuracy": 0.023532436394804658, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.767_0.233": {"average_accuracy": 0.5723270440251572, "std_accuracy": 0.023532436394804658, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.250_0.750": {"average_accuracy": 0.5974842767295597, "std_accuracy": 0.05410267463548821, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.673_0.327": {"average_accuracy": 0.5974842767295597, "std_accuracy": 0.038769899389741985, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.345_0.655": {"average_accuracy": 0.5723270440251572, "std_accuracy": 0.1025755121402521, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.194_0.806": {"average_accuracy": 0.5723270440251572, "std_accuracy": 0.038769899389741985, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.797_0.203": {"average_accuracy": 0.5283018867924528, "std_accuracy": 0.026683274761756505, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.235_0.765": {"average_accuracy": 0.5723270440251572, "std_accuracy": 0.058324644625759106, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}}, "recommendation": "EXCELLENT - High accuracy (0.704) with statistical significance"}, "Clay_Set2_Mid": {"optimal_balance": {"historical_ratio": 0.2076620504440703, "momentum_ratio": 0.7923379495559297, "accuracy": 0.6814800888888891, "confidence_interval_95": [0.6444444444444445, 0.7111111111111111], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 135}, "cross_validation_info": {"configured_folds": 5, "valid_folds": 3, "skipped_folds": 2, "fold_usage_rate": 0.6}, "all_ratios_tested": {"0.737_0.263": {"average_accuracy": 0.6074074074074075, "std_accuracy": 0.14093553770696637, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.247_0.753": {"average_accuracy": 0.6148148148148147, "std_accuracy": 0.08950404424884865, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.724_0.276": {"average_accuracy": 0.6, "std_accuracy": 0.0, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.577_0.423": {"average_accuracy": 0.5555555555555556, "std_accuracy": 0.054433105395181744, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.457_0.543": {"average_accuracy": 0.5925925925925927, "std_accuracy": 0.027715980642769925, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.180_0.820": {"average_accuracy": 0.6222222222222222, "std_accuracy": 0.03142696805273544, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.467_0.533": {"average_accuracy": 0.6148148148148148, "std_accuracy": 0.010475656017578498, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.367_0.633": {"average_accuracy": 0.6074074074074073, "std_accuracy": 0.05832598425193929, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.214_0.786": {"average_accuracy": 0.6296296296296295, "std_accuracy": 0.04566232594791831, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.621_0.379": {"average_accuracy": 0.5851851851851851, "std_accuracy": 0.03777051491550212, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.109_0.891": {"average_accuracy": 0.6222222222222222, "std_accuracy": 0.048005486654873024, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.900_0.100": {"average_accuracy": 0.562962962962963, "std_accuracy": 0.09993138935727434, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.151_0.849": {"average_accuracy": 0.6222222222222221, "std_accuracy": 0.07908946853356524, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.100_0.900": {"average_accuracy": 0.6074074074074075, "std_accuracy": 0.027715980642769977, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.296_0.704": {"average_accuracy": 0.5555555555555556, "std_accuracy": 0.01814436846506056, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.794_0.206": {"average_accuracy": 0.5185185185185185, "std_accuracy": 0.08380524814062779, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.501_0.499": {"average_accuracy": 0.6444444444444444, "std_accuracy": 0.01814436846506056, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.520_0.480": {"average_accuracy": 0.5703703703703703, "std_accuracy": 0.010475656017578444, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.490_0.510": {"average_accuracy": 0.6148148148148148, "std_accuracy": 0.045662325947918324, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.127_0.873": {"average_accuracy": 0.5777777777777778, "std_accuracy": 0.03628873693012116, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.323_0.677": {"average_accuracy": 0.5407407407407407, "std_accuracy": 0.027715980642769925, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.199_0.801": {"average_accuracy": 0.6, "std_accuracy": 0.06542045086168771, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.227_0.773": {"average_accuracy": 0.6296296296296297, "std_accuracy": 0.05832598425193937, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.165_0.835": {"average_accuracy": 0.6518518518518518, "std_accuracy": 0.037770514915502075, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.396_0.604": {"average_accuracy": 0.6592592592592593, "std_accuracy": 0.05237828008789238, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.405_0.595": {"average_accuracy": 0.5925925925925926, "std_accuracy": 0.0818174890162019, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.389_0.611": {"average_accuracy": 0.6518518518518519, "std_accuracy": 0.010475656017578444, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.676_0.324": {"average_accuracy": 0.6444444444444444, "std_accuracy": 0.031426968052735385, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.665_0.335": {"average_accuracy": 0.6518518518518518, "std_accuracy": 0.09132465189583668, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.653_0.347": {"average_accuracy": 0.5777777777777778, "std_accuracy": 0.03142696805273544, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.670_0.330": {"average_accuracy": 0.6666666666666666, "std_accuracy": 0.018144368465060515, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.849_0.151": {"average_accuracy": 0.637037037037037, "std_accuracy": 0.05237828008789243, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.835_0.165": {"average_accuracy": 0.5333333333333333, "std_accuracy": 0.048005486654873045, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.859_0.141": {"average_accuracy": 0.5925925925925926, "std_accuracy": 0.09310966733315951, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.694_0.306": {"average_accuracy": 0.6074074074074074, "std_accuracy": 0.041902624070313935, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.171_0.829": {"average_accuracy": 0.5777777777777778, "std_accuracy": 0.05443310539518177, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.160_0.840": {"average_accuracy": 0.5407407407407407, "std_accuracy": 0.04566232594791834, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.143_0.857": {"average_accuracy": 0.5777777777777778, "std_accuracy": 0.08314794192830977, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.382_0.618": {"average_accuracy": 0.5333333333333333, "std_accuracy": 0.06285393610547088, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.672_0.328": {"average_accuracy": 0.6666666666666666, "std_accuracy": 0.03142696805273544, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.188_0.812": {"average_accuracy": 0.6518518518518518, "std_accuracy": 0.06372092790401945, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.353_0.647": {"average_accuracy": 0.5925925925925926, "std_accuracy": 0.010475656017578498, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.264_0.736": {"average_accuracy": 0.5185185185185185, "std_accuracy": 0.03777051491550214, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.392_0.608": {"average_accuracy": 0.674074074074074, "std_accuracy": 0.08574694002066832, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.757_0.243": {"average_accuracy": 0.5555555555555555, "std_accuracy": 0.03142696805273544, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.432_0.568": {"average_accuracy": 0.5555555555555556, "std_accuracy": 0.01814436846506056, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.548_0.452": {"average_accuracy": 0.6444444444444445, "std_accuracy": 0.07257747386024228, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.557_0.443": {"average_accuracy": 0.5851851851851851, "std_accuracy": 0.1063162969956098, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.540_0.460": {"average_accuracy": 0.5407407407407409, "std_accuracy": 0.10317324649766012, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.237_0.763": {"average_accuracy": 0.6, "std_accuracy": 0.018144368465060606, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.708_0.292": {"average_accuracy": 0.6222222222222222, "std_accuracy": 0.018144368465060606, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.601_0.399": {"average_accuracy": 0.5481481481481482, "std_accuracy": 0.0733295921230494, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.879_0.121": {"average_accuracy": 0.5111111111111111, "std_accuracy": 0.07908946853356529, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.508_0.492": {"average_accuracy": 0.5925925925925926, "std_accuracy": 0.04566232594791836, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.478_0.522": {"average_accuracy": 0.5111111111111111, "std_accuracy": 0.0960109733097461, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.636_0.364": {"average_accuracy": 0.5259259259259259, "std_accuracy": 0.04566232594791834, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.814_0.186": {"average_accuracy": 0.5185185185185185, "std_accuracy": 0.06372092790401945, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.774_0.226": {"average_accuracy": 0.5555555555555556, "std_accuracy": 0.10102356812582114, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.340_0.660": {"average_accuracy": 0.5481481481481482, "std_accuracy": 0.08574694002066834, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.221_0.779": {"average_accuracy": 0.6222222222222222, "std_accuracy": 0.048005486654873024, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.496_0.504": {"average_accuracy": 0.6444444444444444, "std_accuracy": 0.06285393610547088, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.281_0.719": {"average_accuracy": 0.637037037037037, "std_accuracy": 0.045662325947918324, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.418_0.582": {"average_accuracy": 0.637037037037037, "std_accuracy": 0.045662325947918324, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.117_0.883": {"average_accuracy": 0.5407407407407407, "std_accuracy": 0.020951312035156995, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.715_0.285": {"average_accuracy": 0.6296296296296297, "std_accuracy": 0.11086392257107977, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.685_0.315": {"average_accuracy": 0.6, "std_accuracy": 0.11036788463519509, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.445_0.555": {"average_accuracy": 0.6370370370370371, "std_accuracy": 0.055431961285539906, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.746_0.254": {"average_accuracy": 0.525925925925926, "std_accuracy": 0.14777731365377775, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.309_0.691": {"average_accuracy": 0.5629629629629629, "std_accuracy": 0.05237828008789241, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.276_0.724": {"average_accuracy": 0.5481481481481482, "std_accuracy": 0.020951312035156943, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.286_0.714": {"average_accuracy": 0.5629629629629629, "std_accuracy": 0.03777051491550211, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.208_0.792": {"average_accuracy": 0.6814814814814815, "std_accuracy": 0.027715980642769915, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.205_0.795": {"average_accuracy": 0.6296296296296297, "std_accuracy": 0.06372092790401945, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.209_0.791": {"average_accuracy": 0.6518518518518518, "std_accuracy": 0.027715980642769877, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.589_0.411": {"average_accuracy": 0.637037037037037, "std_accuracy": 0.045662325947918324, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.701_0.299": {"average_accuracy": 0.6222222222222222, "std_accuracy": 0.07257747386024233, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.360_0.640": {"average_accuracy": 0.5851851851851851, "std_accuracy": 0.045662325947918365, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.192_0.808": {"average_accuracy": 0.5925925925925926, "std_accuracy": 0.055431961285539864, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.612_0.388": {"average_accuracy": 0.5259259259259259, "std_accuracy": 0.05832598425193934, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.413_0.587": {"average_accuracy": 0.6296296296296297, "std_accuracy": 0.03777051491550213, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.731_0.269": {"average_accuracy": 0.5703703703703703, "std_accuracy": 0.06372092790401948, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.184_0.816": {"average_accuracy": 0.6148148148148148, "std_accuracy": 0.12872701627394642, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.393_0.607": {"average_accuracy": 0.6592592592592593, "std_accuracy": 0.03777051491550212, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.398_0.602": {"average_accuracy": 0.5407407407407407, "std_accuracy": 0.0733295921230494, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.680_0.320": {"average_accuracy": 0.6222222222222222, "std_accuracy": 0.018144368465060606, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.664_0.336": {"average_accuracy": 0.6518518518518518, "std_accuracy": 0.037770514915502075, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.668_0.332": {"average_accuracy": 0.6148148148148148, "std_accuracy": 0.05832598425193934, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.219_0.781": {"average_accuracy": 0.674074074074074, "std_accuracy": 0.041902624070313886, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}}, "recommendation": "EXCELLENT - High accuracy (0.681) with statistical significance"}}, "overall_summary": {"overall_accuracy": 0.6015048281557714, "contexts_tested": 2, "statistically_significant_contexts": 2, "system_reliability_score": 1.0, "cross_validation_summary": {"total_configured_folds": 10, "total_valid_folds": 6, "total_skipped_folds": 4, "overall_fold_usage_rate": 0.6}, "optimal_ratios_by_context": {"Clay_Set1_Mid": {"historical_ratio": 0.24674783189293106, "momentum_ratio": 0.753252168107069, "accuracy": 0.7044374528301885}, "Clay_Set2_Mid": {"historical_ratio": 0.2076620504440703, "momentum_ratio": 0.7923379495559297, "accuracy": 0.6814800888888891}}}, "recommendations": ["⚠️ VALIDATION_WARNING: 4 folds skipped due to insufficient sample sizes", "GOOD: Balance ratio optimization shows good reliability across contexts.", "IMPLEMENTATION: Apply context-specific optimal ratios:", "  • Clay_Set1_Mid: 0.2/0.8 (accuracy: 0.704, 3/5 folds)", "  • Clay_Set2_Mid: 0.2/0.8 (accuracy: 0.681, 3/5 folds)"]}