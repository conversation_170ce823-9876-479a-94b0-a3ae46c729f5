{"validation_timestamp": "2025-07-29T11:22:00.463851", "validation_type": "historical_momentum_balance_ratios", "configuration": {"n_splits": 5, "test_size": 0.2, "bootstrap_samples": 1000000, "temporal_validation": true, "min_context_sample_size": 150, "significance_threshold": 0.05, "balance_ratios_tested": [[0.1, 0.9], [0.15, 0.85], [0.2, 0.8], [0.25, 0.75], [0.3, 0.7], [0.35, 0.65], [0.4, 0.6], [0.45, 0.55], [0.5, 0.5], [0.55, 0.44999999999999996], [0.6, 0.4], [0.65, 0.35], [0.7, 0.30000000000000004], [0.75, 0.25], [0.8, 0.19999999999999996], [0.85, 0.15000000000000002], [0.9, 0.09999999999999998]]}, "context_results": {"Clay_Set1_Mid": {"optimal_balance": {"historical_ratio": 0.351912872053706, "momentum_ratio": 0.648087127946294, "accuracy": 0.6465542566037734, "confidence_interval_95": [0.6075471698113207, 0.6679245283018865], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 159}, "cross_validation_info": {"configured_folds": 5, "valid_folds": 3, "skipped_folds": 2, "fold_usage_rate": 0.6}, "all_ratios_tested": {"0.737_0.263": {"average_accuracy": 0.5886792452830188, "std_accuracy": 0.010673309904702602, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.247_0.753": {"average_accuracy": 0.6025157232704401, "std_accuracy": 0.007115539936468428, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.724_0.276": {"average_accuracy": 0.6037735849056604, "std_accuracy": 0.024064310024791178, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.577_0.423": {"average_accuracy": 0.6238993710691824, "std_accuracy": 0.0299254773669694, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.457_0.543": {"average_accuracy": 0.6088050314465409, "std_accuracy": 0.009412974557921902, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.180_0.820": {"average_accuracy": 0.6062893081761006, "std_accuracy": 0.022711283126123, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.467_0.533": {"average_accuracy": 0.5937106918238994, "std_accuracy": 0.01857210447828096, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.367_0.633": {"average_accuracy": 0.6088050314465409, "std_accuracy": 0.01751998525431968, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.214_0.786": {"average_accuracy": 0.6025157232704402, "std_accuracy": 0.03265598738552746, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.621_0.379": {"average_accuracy": 0.6251572327044025, "std_accuracy": 0.019808824840281312, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.602_0.398": {"average_accuracy": 0.6314465408805032, "std_accuracy": 0.02912160227117037, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.900_0.100": {"average_accuracy": 0.5962264150943396, "std_accuracy": 0.013430287109473344, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.100_0.900": {"average_accuracy": 0.6188679245283019, "std_accuracy": 0.03499478677545548, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.643_0.357": {"average_accuracy": 0.6163522012578616, "std_accuracy": 0.028462159745873607, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.557_0.443": {"average_accuracy": 0.5987421383647797, "std_accuracy": 0.02583979696122535, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.311_0.689": {"average_accuracy": 0.6012578616352201, "std_accuracy": 0.003557769968234227, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.811_0.189": {"average_accuracy": 0.6339622641509434, "std_accuracy": 0.021346619809405255, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.830_0.170": {"average_accuracy": 0.5874213836477987, "std_accuracy": 0.0077539798779483965, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.606_0.394": {"average_accuracy": 0.6025157232704401, "std_accuracy": 0.03162215116975225, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.665_0.335": {"average_accuracy": 0.6150943396226415, "std_accuracy": 0.038606312586724034, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.132_0.868": {"average_accuracy": 0.5949685534591195, "std_accuracy": 0.031168582876387033, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.404_0.596": {"average_accuracy": 0.5861635220125786, "std_accuracy": 0.015198799966785642, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.524_0.476": {"average_accuracy": 0.6251572327044025, "std_accuracy": 0.03597572238006321, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.536_0.464": {"average_accuracy": 0.6062893081761006, "std_accuracy": 0.02004701566101785, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.660_0.340": {"average_accuracy": 0.5811320754716981, "std_accuracy": 0.018741716258806683, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.785_0.215": {"average_accuracy": 0.5761006289308176, "std_accuracy": 0.012452194888819703, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.518_0.482": {"average_accuracy": 0.6163522012578616, "std_accuracy": 0.017788849841170976, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.293_0.707": {"average_accuracy": 0.5974842767295597, "std_accuracy": 0.02164106985419528, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.592_0.408": {"average_accuracy": 0.5647798742138365, "std_accuracy": 0.030397599933571242, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.807_0.193": {"average_accuracy": 0.5849056603773585, "std_accuracy": 0.020204249565042777, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.629_0.371": {"average_accuracy": 0.6075471698113207, "std_accuracy": 0.05265018751309767, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.111_0.889": {"average_accuracy": 0.6050314465408805, "std_accuracy": 0.03236397567363171, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.510_0.490": {"average_accuracy": 0.5723270440251572, "std_accuracy": 0.046761334076581396, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.615_0.385": {"average_accuracy": 0.569811320754717, "std_accuracy": 0.005336654952351275, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.570_0.430": {"average_accuracy": 0.6163522012578616, "std_accuracy": 0.03086250099719571, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.528_0.472": {"average_accuracy": 0.5949685534591195, "std_accuracy": 0.013893535870675839, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.352_0.648": {"average_accuracy": 0.6465408805031446, "std_accuracy": 0.02761572125798757, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.348_0.652": {"average_accuracy": 0.6176100628930817, "std_accuracy": 0.03597572238006318, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.356_0.644": {"average_accuracy": 0.6100628930817611, "std_accuracy": 0.022289364964363987, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.341_0.659": {"average_accuracy": 0.6037735849056602, "std_accuracy": 0.035533199727862966, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.438_0.562": {"average_accuracy": 0.620125786163522, "std_accuracy": 0.008894424920585554, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.428_0.572": {"average_accuracy": 0.5836477987421383, "std_accuracy": 0.023125504793522277, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.686_0.314": {"average_accuracy": 0.5924528301886792, "std_accuracy": 0.018486715039873036, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.380_0.620": {"average_accuracy": 0.6, "std_accuracy": 0.037356584666459136, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.161_0.839": {"average_accuracy": 0.5786163522012577, "std_accuracy": 0.004706487278960882, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.328_0.672": {"average_accuracy": 0.5559748427672955, "std_accuracy": 0.007753979877948403, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.359_0.641": {"average_accuracy": 0.5924528301886792, "std_accuracy": 0.005336654952351248, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.196_0.804": {"average_accuracy": 0.6088050314465409, "std_accuracy": 0.01423107987293683, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.267_0.733": {"average_accuracy": 0.6025157232704402, "std_accuracy": 0.012452194888819702, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.861_0.139": {"average_accuracy": 0.6138364779874214, "std_accuracy": 0.025094260809132105, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.850_0.150": {"average_accuracy": 0.6150943396226415, "std_accuracy": 0.017154945530799864, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.873_0.127": {"average_accuracy": 0.6138364779874214, "std_accuracy": 0.0256554440935486, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.447_0.553": {"average_accuracy": 0.6012578616352201, "std_accuracy": 0.0017788849841171264, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.231_0.769": {"average_accuracy": 0.620125786163522, "std_accuracy": 0.02004701566101789, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.707_0.293": {"average_accuracy": 0.5798742138364781, "std_accuracy": 0.028628444469788808, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.760_0.240": {"average_accuracy": 0.620125786163522, "std_accuracy": 0.03294541095272656, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.768_0.232": {"average_accuracy": 0.5836477987421383, "std_accuracy": 0.0427302836514794, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.486_0.514": {"average_accuracy": 0.6188679245283017, "std_accuracy": 0.039337096279526716, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.751_0.249": {"average_accuracy": 0.5698113207547171, "std_accuracy": 0.013430287109473396, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.494_0.506": {"average_accuracy": 0.5710691823899371, "std_accuracy": 0.00641386102338715, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.224_0.776": {"average_accuracy": 0.5886792452830188, "std_accuracy": 0.024064310024791254, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.818_0.182": {"average_accuracy": 0.6150943396226415, "std_accuracy": 0.027730072559809558, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.898_0.102": {"average_accuracy": 0.6264150943396226, "std_accuracy": 0.02522004736574015, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.305_0.695": {"average_accuracy": 0.5836477987421383, "std_accuracy": 0.02656441771313569, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.677_0.323": {"average_accuracy": 0.6452830188679245, "std_accuracy": 0.00815187509233693, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.203_0.797": {"average_accuracy": 0.5924528301886792, "std_accuracy": 0.022218266330384515, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.437_0.563": {"average_accuracy": 0.5773584905660377, "std_accuracy": 0.00815187509233693, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.730_0.270": {"average_accuracy": 0.5937106918238994, "std_accuracy": 0.02862844446978878, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.500_0.500": {"average_accuracy": 0.6138364779874214, "std_accuracy": 0.054800052605765995, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.769_0.231": {"average_accuracy": 0.6163522012578616, "std_accuracy": 0.027787071741351546, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.258_0.742": {"average_accuracy": 0.6276729559748427, "std_accuracy": 0.01696948121161272, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.586_0.414": {"average_accuracy": 0.5911949685534591, "std_accuracy": 0.00990441242014066, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.248_0.752": {"average_accuracy": 0.5886792452830188, "std_accuracy": 0.013430287109473365, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.670_0.330": {"average_accuracy": 0.6188679245283019, "std_accuracy": 0.014119461836882834, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.806_0.194": {"average_accuracy": 0.5937106918238994, "std_accuracy": 0.004706487278960892, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}}, "recommendation": "GOOD - Moderate accuracy (0.647) with statistical significance"}, "Clay_Set2_Mid": {"optimal_balance": {"historical_ratio": 0.3469101923512755, "momentum_ratio": 0.6530898076487245, "accuracy": 0.644450346666667, "confidence_interval_95": [0.6044444444444445, 0.6711111111111112], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 135}, "cross_validation_info": {"configured_folds": 5, "valid_folds": 3, "skipped_folds": 2, "fold_usage_rate": 0.6}, "all_ratios_tested": {"0.737_0.263": {"average_accuracy": 0.5940740740740741, "std_accuracy": 0.0075541029831003685, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.247_0.753": {"average_accuracy": 0.6044444444444443, "std_accuracy": 0.02020471362516422, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.724_0.276": {"average_accuracy": 0.5777777777777777, "std_accuracy": 0.01451549477204851, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.577_0.423": {"average_accuracy": 0.5762962962962963, "std_accuracy": 0.014665918424609865, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.457_0.543": {"average_accuracy": 0.6029629629629629, "std_accuracy": 0.034298776008267313, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.180_0.820": {"average_accuracy": 0.6207407407407407, "std_accuracy": 0.021263259399122013, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.467_0.533": {"average_accuracy": 0.6088888888888889, "std_accuracy": 0.015817893706713073, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.367_0.633": {"average_accuracy": 0.6, "std_accuracy": 0.025402115851084817, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.214_0.786": {"average_accuracy": 0.6266666666666666, "std_accuracy": 0.03100511042437975, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.621_0.379": {"average_accuracy": 0.642962962962963, "std_accuracy": 0.005543196128553983, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.633_0.367": {"average_accuracy": 0.5925925925925926, "std_accuracy": 0.024701232593382257, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.615_0.385": {"average_accuracy": 0.6088888888888889, "std_accuracy": 0.009601097330974567, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.205_0.795": {"average_accuracy": 0.5896296296296296, "std_accuracy": 0.03880237289987793, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.224_0.776": {"average_accuracy": 0.6162962962962962, "std_accuracy": 0.024162231748592704, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.605_0.395": {"average_accuracy": 0.5822222222222222, "std_accuracy": 0.015817893706713104, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.900_0.100": {"average_accuracy": 0.6044444444444443, "std_accuracy": 0.034617248727549364, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.100_0.900": {"average_accuracy": 0.5525925925925925, "std_accuracy": 0.012744185580803835, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.624_0.376": {"average_accuracy": 0.6162962962962962, "std_accuracy": 0.013738694067401027, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.170_0.830": {"average_accuracy": 0.5762962962962962, "std_accuracy": 0.010475656017578498, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.233_0.767": {"average_accuracy": 0.5911111111111111, "std_accuracy": 0.03925227051701264, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.189_0.811": {"average_accuracy": 0.6074074074074073, "std_accuracy": 0.04205946538245449, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.482_0.518": {"average_accuracy": 0.5644444444444444, "std_accuracy": 0.013084090172337547, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.263_0.737": {"average_accuracy": 0.6, "std_accuracy": 0.009601097330974567, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.440_0.560": {"average_accuracy": 0.5925925925925926, "std_accuracy": 0.009132465189583721, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.218_0.782": {"average_accuracy": 0.6118518518518518, "std_accuracy": 0.01510820596620089, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.345_0.655": {"average_accuracy": 0.5881481481481482, "std_accuracy": 0.008380524814062812, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.388_0.612": {"average_accuracy": 0.6162962962962963, "std_accuracy": 0.01826493037916727, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.399_0.601": {"average_accuracy": 0.6148148148148148, "std_accuracy": 0.04832446349718547, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.410_0.590": {"average_accuracy": 0.6029629629629629, "std_accuracy": 0.016363497803240357, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.379_0.621": {"average_accuracy": 0.5940740740740741, "std_accuracy": 0.021263259399121912, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.287_0.713": {"average_accuracy": 0.5925925925925927, "std_accuracy": 0.01998627787145489, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.815_0.185": {"average_accuracy": 0.5985185185185184, "std_accuracy": 0.0163634978032404, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.785_0.215": {"average_accuracy": 0.5822222222222222, "std_accuracy": 0.028342409584754036, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.842_0.158": {"average_accuracy": 0.5659259259259259, "std_accuracy": 0.029555462730755544, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.676_0.324": {"average_accuracy": 0.6311111111111111, "std_accuracy": 0.02207357692703903, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.668_0.332": {"average_accuracy": 0.6162962962962962, "std_accuracy": 0.029106492895390374, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.682_0.318": {"average_accuracy": 0.5955555555555555, "std_accuracy": 0.04354648431614535, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.659_0.341": {"average_accuracy": 0.5955555555555555, "std_accuracy": 0.025141574442188334, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.530_0.470": {"average_accuracy": 0.5866666666666666, "std_accuracy": 0.03461724872754939, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.315_0.685": {"average_accuracy": 0.5866666666666667, "std_accuracy": 0.009601097330974669, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.136_0.864": {"average_accuracy": 0.5940740740740741, "std_accuracy": 0.029331836849219705, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.759_0.241": {"average_accuracy": 0.6133333333333334, "std_accuracy": 0.014515494772048465, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.871_0.129": {"average_accuracy": 0.6, "std_accuracy": 0.02379611615438379, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.870_0.130": {"average_accuracy": 0.6014814814814815, "std_accuracy": 0.018264930379167366, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.300_0.700": {"average_accuracy": 0.5970370370370371, "std_accuracy": 0.012744185580803926, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.800_0.200": {"average_accuracy": 0.5911111111111111, "std_accuracy": 0.027397395568751003, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.650_0.350": {"average_accuracy": 0.5659259259259258, "std_accuracy": 0.011665196850387808, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.490_0.510": {"average_accuracy": 0.6118518518518519, "std_accuracy": 0.03486993272871062, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.565_0.435": {"average_accuracy": 0.6177777777777779, "std_accuracy": 0.03100511042437979, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.390_0.610": {"average_accuracy": 0.605925925925926, "std_accuracy": 0.008380524814062762, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.868_0.132": {"average_accuracy": 0.6103703703703703, "std_accuracy": 0.017149388004133678, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.788_0.212": {"average_accuracy": 0.5659259259259259, "std_accuracy": 0.0358016176995395, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.123_0.877": {"average_accuracy": 0.5570370370370371, "std_accuracy": 0.018621933466631935, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.377_0.623": {"average_accuracy": 0.5955555555555555, "std_accuracy": 0.020204713625164215, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.448_0.552": {"average_accuracy": 0.5925925925925926, "std_accuracy": 0.014665918424609918, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.105_0.895": {"average_accuracy": 0.6296296296296297, "std_accuracy": 0.0020951312035156575, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.801_0.199": {"average_accuracy": 0.5955555555555556, "std_accuracy": 0.01451549477204851, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.423_0.577": {"average_accuracy": 0.5985185185185186, "std_accuracy": 0.011665196850387858, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.647_0.353": {"average_accuracy": 0.6162962962962962, "std_accuracy": 0.011665196850387877, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.208_0.792": {"average_accuracy": 0.6103703703703703, "std_accuracy": 0.01826493037916736, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.896_0.104": {"average_accuracy": 0.6162962962962965, "std_accuracy": 0.01714938800413364, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.671_0.329": {"average_accuracy": 0.6, "std_accuracy": 0.009601097330974584, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.521_0.479": {"average_accuracy": 0.5940740740740741, "std_accuracy": 0.023330393700775783, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.739_0.261": {"average_accuracy": 0.6088888888888888, "std_accuracy": 0.03682904561379572, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.406_0.594": {"average_accuracy": 0.5792592592592593, "std_accuracy": 0.03486993272871052, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.795_0.205": {"average_accuracy": 0.5896296296296296, "std_accuracy": 0.03997255574290977, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.827_0.173": {"average_accuracy": 0.6222222222222221, "std_accuracy": 0.03461724872754949, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.356_0.644": {"average_accuracy": 0.6, "std_accuracy": 0.01581789370671302, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.677_0.323": {"average_accuracy": 0.6059259259259259, "std_accuracy": 0.01466591842460983, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.729_0.271": {"average_accuracy": 0.5881481481481482, "std_accuracy": 0.0344902125239195, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.597_0.403": {"average_accuracy": 0.5896296296296296, "std_accuracy": 0.005543196128553934, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.574_0.426": {"average_accuracy": 0.6014814814814814, "std_accuracy": 0.022172784514215945, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.717_0.283": {"average_accuracy": 0.5896296296296296, "std_accuracy": 0.03292757151243386, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.735_0.265": {"average_accuracy": 0.5599999999999999, "std_accuracy": 0.020204713625164225, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.347_0.653": {"average_accuracy": 0.6444444444444445, "std_accuracy": 0.028803291992923856, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.853_0.147": {"average_accuracy": 0.6296296296296297, "std_accuracy": 0.01714938800413369, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.353_0.647": {"average_accuracy": 0.5599999999999999, "std_accuracy": 0.01451549477204842, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.783_0.217": {"average_accuracy": 0.6192592592592592, "std_accuracy": 0.036529860758334684, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.733_0.267": {"average_accuracy": 0.6222222222222222, "std_accuracy": 0.010886621079036372, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.500_0.500": {"average_accuracy": 0.6118518518518519, "std_accuracy": 0.0233303937007758, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.432_0.568": {"average_accuracy": 0.6014814814814815, "std_accuracy": 0.014665918424609879, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.877_0.123": {"average_accuracy": 0.5777777777777778, "std_accuracy": 0.015817893706713052, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.508_0.492": {"average_accuracy": 0.597037037037037, "std_accuracy": 0.01790080884976975, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.209_0.791": {"average_accuracy": 0.6118518518518519, "std_accuracy": 0.03880237289987795, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.203_0.797": {"average_accuracy": 0.5674074074074074, "std_accuracy": 0.024162231748592725, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.437_0.563": {"average_accuracy": 0.5896296296296296, "std_accuracy": 0.011086392257107955, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.730_0.270": {"average_accuracy": 0.5733333333333334, "std_accuracy": 0.02880329199292377, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.769_0.231": {"average_accuracy": 0.6266666666666666, "std_accuracy": 0.023796116154383734, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.258_0.742": {"average_accuracy": 0.6074074074074074, "std_accuracy": 0.011086392257107966, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.586_0.414": {"average_accuracy": 0.6237037037037036, "std_accuracy": 0.02548837116160781, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.248_0.752": {"average_accuracy": 0.6074074074074073, "std_accuracy": 0.0327269956064808, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.670_0.330": {"average_accuracy": 0.6222222222222221, "std_accuracy": 0.019202194661949203, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}, "0.806_0.194": {"average_accuracy": 0.5777777777777777, "std_accuracy": 0.03325917677132392, "total_folds": 3, "configured_folds": 5, "valid_folds": 3, "skipped_folds": 0}}, "recommendation": "GOOD - Moderate accuracy (0.644) with statistical significance"}}, "overall_summary": {"overall_accuracy": 0.6004407507237696, "contexts_tested": 2, "statistically_significant_contexts": 2, "system_reliability_score": 1.0, "cross_validation_summary": {"total_configured_folds": 10, "total_valid_folds": 6, "total_skipped_folds": 4, "overall_fold_usage_rate": 0.6}, "optimal_ratios_by_context": {"Clay_Set1_Mid": {"historical_ratio": 0.351912872053706, "momentum_ratio": 0.648087127946294, "accuracy": 0.6465542566037734}, "Clay_Set2_Mid": {"historical_ratio": 0.3469101923512755, "momentum_ratio": 0.6530898076487245, "accuracy": 0.644450346666667}}}, "recommendations": ["⚠️ VALIDATION_WARNING: 4 folds skipped due to insufficient sample sizes", "GOOD: Balance ratio optimization shows good reliability across contexts.", "IMPLEMENTATION: Apply context-specific optimal ratios:", "  • Clay_Set1_Mid: 0.4/0.6 (accuracy: 0.647, 3/5 folds)", "  • Clay_Set2_Mid: 0.3/0.7 (accuracy: 0.644, 3/5 folds)"]}