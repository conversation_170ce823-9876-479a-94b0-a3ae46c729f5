{"validation_timestamp": "2025-07-29T09:57:07.261307", "validation_type": "historical_momentum_balance_ratios", "configuration": {"n_splits": 5, "test_size": 0.2, "bootstrap_samples": 1000000, "temporal_validation": true, "min_context_sample_size": 150, "significance_threshold": 0.05, "balance_ratios_tested": [[0.1, 0.9], [0.15, 0.85], [0.2, 0.8], [0.25, 0.75], [0.3, 0.7], [0.35, 0.65], [0.4, 0.6], [0.45, 0.55], [0.5, 0.5], [0.55, 0.44999999999999996], [0.6, 0.4], [0.65, 0.35], [0.7, 0.30000000000000004], [0.75, 0.25], [0.8, 0.19999999999999996], [0.85, 0.15000000000000002], [0.9, 0.09999999999999998]]}, "context_results": {"Clay_Set1_Mid": {"optimal_balance": {"historical_ratio": 0.7372343894881864, "momentum_ratio": 0.2627656105118136, "accuracy": 0.6299589278911566, "confidence_interval_95": [0.5836734693877551, 0.6653061224489797], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 147}, "all_ratios_tested": {"0.737_0.263": {"average_accuracy": 0.6299319727891156, "std_accuracy": 0.03420355126524229, "total_folds": 3}, "0.247_0.753": {"average_accuracy": 0.5795918367346938, "std_accuracy": 0.003332639105827448, "total_folds": 3}, "0.724_0.276": {"average_accuracy": 0.564625850340136, "std_accuracy": 0.02523433604216516, "total_folds": 3}, "0.577_0.423": {"average_accuracy": 0.5673469387755102, "std_accuracy": 0.024032002357354695, "total_folds": 3}, "0.457_0.543": {"average_accuracy": 0.5673469387755102, "std_accuracy": 0.015272070966424245, "total_folds": 3}, "0.180_0.820": {"average_accuracy": 0.5727891156462585, "std_accuracy": 0.013468700594029458, "total_folds": 3}, "0.467_0.533": {"average_accuracy": 0.6108843537414965, "std_accuracy": 0.013874883030184528, "total_folds": 3}, "0.367_0.633": {"average_accuracy": 0.5850340136054422, "std_accuracy": 0.025234336042165195, "total_folds": 3}, "0.214_0.786": {"average_accuracy": 0.601360544217687, "std_accuracy": 0.010181380644282857, "total_folds": 3}, "0.621_0.379": {"average_accuracy": 0.5782312925170069, "std_accuracy": 0.025886119170667223, "total_folds": 3}, "0.743_0.257": {"average_accuracy": 0.6027210884353741, "std_accuracy": 0.02268480544290213, "total_folds": 3}, "0.750_0.250": {"average_accuracy": 0.5755102040816326, "std_accuracy": 0.01763466856709624, "total_folds": 3}, "0.735_0.265": {"average_accuracy": 0.6258503401360543, "std_accuracy": 0.006937441515092155, "total_folds": 3}, "0.477_0.523": {"average_accuracy": 0.5918367346938775, "std_accuracy": 0.032822688657999526, "total_folds": 3}, "0.227_0.773": {"average_accuracy": 0.6244897959183673, "std_accuracy": 0.013330556423309745, "total_folds": 3}, "0.233_0.767": {"average_accuracy": 0.6244897959183673, "std_accuracy": 0.01452663707759364, "total_folds": 3}, "0.899_0.101": {"average_accuracy": 0.6231292517006802, "std_accuracy": 0.006937441515092169, "total_folds": 3}, "0.891_0.109": {"average_accuracy": 0.5782312925170068, "std_accuracy": 0.022684805442902113, "total_folds": 3}, "0.100_0.900": {"average_accuracy": 0.5986394557823128, "std_accuracy": 0.005090690322141389, "total_folds": 3}, "0.117_0.883": {"average_accuracy": 0.6081632653061224, "std_accuracy": 0.03463380152750444, "total_folds": 3}, "0.130_0.870": {"average_accuracy": 0.5741496598639456, "std_accuracy": 0.03096546034487356, "total_folds": 3}, "0.900_0.100": {"average_accuracy": 0.6163265306122448, "std_accuracy": 0.017634668567096223, "total_folds": 3}, "0.335_0.665": {"average_accuracy": 0.6136054421768707, "std_accuracy": 0.02142587176601853, "total_folds": 3}, "0.225_0.775": {"average_accuracy": 0.5931972789115646, "std_accuracy": 0.02501330110319758, "total_folds": 3}, "0.324_0.676": {"average_accuracy": 0.5972789115646259, "std_accuracy": 0.05418315034487042, "total_folds": 3}, "0.345_0.655": {"average_accuracy": 0.5877551020408164, "std_accuracy": 0.009997917317482343, "total_folds": 3}, "0.821_0.179": {"average_accuracy": 0.6108843537414965, "std_accuracy": 0.020088194639773304, "total_folds": 3}, "0.808_0.192": {"average_accuracy": 0.6040816326530613, "std_accuracy": 0.03179135087223922, "total_folds": 3}, "0.833_0.167": {"average_accuracy": 0.6122448979591836, "std_accuracy": 0.009997917317482343, "total_folds": 3}, "0.844_0.156": {"average_accuracy": 0.6204081632653061, "std_accuracy": 0.003332639105827448, "total_folds": 3}, "0.852_0.148": {"average_accuracy": 0.5782312925170068, "std_accuracy": 0.03500593287147922, "total_folds": 3}, "0.793_0.207": {"average_accuracy": 0.601360544217687, "std_accuracy": 0.01895018813222333, "total_folds": 3}}, "recommendation": "GOOD - Moderate accuracy (0.630) with statistical significance"}, "Clay_Set2_Mid": {"optimal_balance": {"historical_ratio": 0.8872183945273328, "momentum_ratio": 0.11278160547266725, "accuracy": 0.6403105100775197, "confidence_interval_95": [0.6372093023255815, 0.6418604651162791], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 129}, "all_ratios_tested": {"0.737_0.263": {"average_accuracy": 0.5953488372093023, "std_accuracy": 0.0037976585159429006, "total_folds": 3}, "0.247_0.753": {"average_accuracy": 0.592248062015504, "std_accuracy": 0.02470911232637091, "total_folds": 3}, "0.724_0.276": {"average_accuracy": 0.5984496124031008, "std_accuracy": 0.03069610833057885, "total_folds": 3}, "0.577_0.423": {"average_accuracy": 0.5922480620155038, "std_accuracy": 0.015348054165289463, "total_folds": 3}, "0.457_0.543": {"average_accuracy": 0.5782945736434109, "std_accuracy": 0.034249181448642654, "total_folds": 3}, "0.180_0.820": {"average_accuracy": 0.6015503875968992, "std_accuracy": 0.03046028326261783, "total_folds": 3}, "0.467_0.533": {"average_accuracy": 0.5875968992248062, "std_accuracy": 0.039527283051106844, "total_folds": 3}, "0.367_0.633": {"average_accuracy": 0.6325581395348837, "std_accuracy": 0.011392975547828747, "total_folds": 3}, "0.214_0.786": {"average_accuracy": 0.5627906976744187, "std_accuracy": 0.01004765999753152, "total_folds": 3}, "0.621_0.379": {"average_accuracy": 0.6, "std_accuracy": 0.023716369830664146, "total_folds": 3}, "0.354_0.646": {"average_accuracy": 0.5891472868217055, "std_accuracy": 0.03424918144864266, "total_folds": 3}, "0.372_0.628": {"average_accuracy": 0.6310077519379845, "std_accuracy": 0.02289119854299759, "total_folds": 3}, "0.383_0.617": {"average_accuracy": 0.5829457364341085, "std_accuracy": 0.007905456610221349, "total_folds": 3}, "0.163_0.837": {"average_accuracy": 0.6139534883720931, "std_accuracy": 0.011392975547828747, "total_folds": 3}, "0.152_0.848": {"average_accuracy": 0.5612403100775194, "std_accuracy": 0.013336938398515708, "total_folds": 3}, "0.170_0.830": {"average_accuracy": 0.6, "std_accuracy": 0.03740259870330175, "total_folds": 3}, "0.900_0.100": {"average_accuracy": 0.6077519379844961, "std_accuracy": 0.0079054566102214, "total_folds": 3}, "0.369_0.631": {"average_accuracy": 0.5984496124031007, "std_accuracy": 0.0342491814486427, "total_folds": 3}, "0.363_0.637": {"average_accuracy": 0.5922480620155038, "std_accuracy": 0.008770316665879633, "total_folds": 3}, "0.887_0.113": {"average_accuracy": 0.6403100775193798, "std_accuracy": 0.0021925791664699082, "total_folds": 3}, "0.883_0.117": {"average_accuracy": 0.5937984496124031, "std_accuracy": 0.035286222253460564, "total_folds": 3}, "0.892_0.108": {"average_accuracy": 0.5689922480620154, "std_accuracy": 0.03274218927433003, "total_folds": 3}, "0.870_0.130": {"average_accuracy": 0.5953488372093024, "std_accuracy": 0.019733212498229277, "total_folds": 3}, "0.641_0.359": {"average_accuracy": 0.5612403100775194, "std_accuracy": 0.014377703093791746, "total_folds": 3}, "0.703_0.297": {"average_accuracy": 0.5968992248062015, "std_accuracy": 0.04183174438211482, "total_folds": 3}, "0.603_0.397": {"average_accuracy": 0.6077519379844961, "std_accuracy": 0.022891198542997548, "total_folds": 3}, "0.100_0.900": {"average_accuracy": 0.5891472868217054, "std_accuracy": 0.03822892404942003, "total_folds": 3}, "0.805_0.195": {"average_accuracy": 0.5937984496124031, "std_accuracy": 0.005801019204300667, "total_folds": 3}, "0.843_0.157": {"average_accuracy": 0.5829457364341085, "std_accuracy": 0.011602038408601334, "total_folds": 3}, "0.681_0.319": {"average_accuracy": 0.5798449612403102, "std_accuracy": 0.05428570976747784, "total_folds": 3}, "0.524_0.476": {"average_accuracy": 0.6015503875968992, "std_accuracy": 0.04401571958628955, "total_folds": 3}, "0.542_0.458": {"average_accuracy": 0.5844961240310078, "std_accuracy": 0.012207764145754708, "total_folds": 3}, "0.506_0.494": {"average_accuracy": 0.6403100775193798, "std_accuracy": 0.017124590724321376, "total_folds": 3}, "0.502_0.498": {"average_accuracy": 0.634108527131783, "std_accuracy": 0.03746680922044838, "total_folds": 3}, "0.509_0.491": {"average_accuracy": 0.6279069767441859, "std_accuracy": 0.03310721938614366, "total_folds": 3}, "0.500_0.500": {"average_accuracy": 0.5937984496124031, "std_accuracy": 0.02441552829150949, "total_folds": 3}, "0.512_0.488": {"average_accuracy": 0.5844961240310077, "std_accuracy": 0.02747293821189041, "total_folds": 3}, "0.492_0.508": {"average_accuracy": 0.6294573643410853, "std_accuracy": 0.029498135799132526, "total_folds": 3}, "0.488_0.512": {"average_accuracy": 0.6217054263565892, "std_accuracy": 0.01873340461022416, "total_folds": 3}, "0.303_0.697": {"average_accuracy": 0.593798449612403, "std_accuracy": 0.0372738458299886, "total_folds": 3}, "0.771_0.229": {"average_accuracy": 0.6046511627906976, "std_accuracy": 0.007595317031885801, "total_folds": 3}, "0.413_0.587": {"average_accuracy": 0.5922480620155038, "std_accuracy": 0.014377703093791798, "total_folds": 3}, "0.769_0.231": {"average_accuracy": 0.6124031007751939, "std_accuracy": 0.025286056481085416, "total_folds": 3}, "0.258_0.742": {"average_accuracy": 0.6077519379844961, "std_accuracy": 0.02441552829150947, "total_folds": 3}, "0.586_0.414": {"average_accuracy": 0.6310077519379845, "std_accuracy": 0.0436868303987764, "total_folds": 3}, "0.670_0.330": {"average_accuracy": 0.6031007751937985, "std_accuracy": 0.036491790064929704, "total_folds": 3}, "0.806_0.194": {"average_accuracy": 0.5767441860465116, "std_accuracy": 0.02738530501186926, "total_folds": 3}}, "recommendation": "GOOD - Moderate accuracy (0.640) with statistical significance"}}, "overall_summary": {"overall_accuracy": 0.5985717690078681, "contexts_tested": 2, "statistically_significant_contexts": 2, "system_reliability_score": 1.0, "optimal_ratios_by_context": {"Clay_Set1_Mid": {"historical_ratio": 0.7372343894881864, "momentum_ratio": 0.2627656105118136, "accuracy": 0.6299589278911566}, "Clay_Set2_Mid": {"historical_ratio": 0.8872183945273328, "momentum_ratio": 0.11278160547266725, "accuracy": 0.6403105100775197}}}, "recommendations": ["GOOD: Balance ratio optimization shows good reliability across contexts.", "IMPLEMENTATION: Apply context-specific optimal ratios:", "  • Clay_Set1_Mid: 0.7/0.3 (accuracy: 0.630)", "  • Clay_Set2_Mid: 0.9/0.1 (accuracy: 0.640)"]}