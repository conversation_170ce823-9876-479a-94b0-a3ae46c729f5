{"validation_timestamp": "2025-07-29T09:47:50.793809", "validation_type": "historical_momentum_balance_ratios", "configuration": {"n_splits": 5, "test_size": 0.2, "bootstrap_samples": 1000000, "temporal_validation": true, "min_context_sample_size": 150, "significance_threshold": 0.05, "balance_ratios_tested": [[0.1, 0.9], [0.15, 0.85], [0.2, 0.8], [0.25, 0.75], [0.3, 0.7], [0.35, 0.65], [0.4, 0.6], [0.45, 0.55], [0.5, 0.5], [0.55, 0.44999999999999996], [0.6, 0.4], [0.65, 0.35], [0.7, 0.30000000000000004], [0.75, 0.25], [0.8, 0.19999999999999996], [0.85, 0.15000000000000002], [0.9, 0.09999999999999998]]}, "context_results": {"Clay_Set1_Mid": {"optimal_balance": {"historical_ratio": 0.456666202282873, "momentum_ratio": 0.543333797717127, "accuracy": 0.6421901183673473, "confidence_interval_95": [0.6, 0.6693877551020407], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 147}, "all_ratios_tested": {"0.737_0.263": {"average_accuracy": 0.5768707482993197, "std_accuracy": 0.05832911866728165, "total_folds": 3}, "0.247_0.753": {"average_accuracy": 0.6231292517006803, "std_accuracy": 0.021165100933474908, "total_folds": 3}, "0.724_0.276": {"average_accuracy": 0.5714285714285715, "std_accuracy": 0.008817334283548129, "total_folds": 3}, "0.577_0.423": {"average_accuracy": 0.5972789115646259, "std_accuracy": 0.0069374415150922205, "total_folds": 3}, "0.457_0.543": {"average_accuracy": 0.6421768707482993, "std_accuracy": 0.03023960649101069, "total_folds": 3}, "0.180_0.820": {"average_accuracy": 0.5972789115646259, "std_accuracy": 0.033381888833701405, "total_folds": 3}, "0.467_0.533": {"average_accuracy": 0.5578231292517007, "std_accuracy": 0.05589833628346703, "total_folds": 3}, "0.367_0.633": {"average_accuracy": 0.5904761904761905, "std_accuracy": 0.005090690322141389, "total_folds": 3}, "0.214_0.786": {"average_accuracy": 0.6000000000000001, "std_accuracy": 0.03179135087223926, "total_folds": 3}, "0.621_0.379": {"average_accuracy": 0.5986394557823128, "std_accuracy": 0.04475342640380552, "total_folds": 3}, "0.450_0.550": {"average_accuracy": 0.6095238095238095, "std_accuracy": 0.005090690322141439, "total_folds": 3}, "0.238_0.762": {"average_accuracy": 0.6081632653061225, "std_accuracy": 0.0328226886579995, "total_folds": 3}, "0.255_0.745": {"average_accuracy": 0.6095238095238095, "std_accuracy": 0.005090690322141428, "total_folds": 3}, "0.441_0.559": {"average_accuracy": 0.6244897959183673, "std_accuracy": 0.03929123558692146, "total_folds": 3}, "0.432_0.568": {"average_accuracy": 0.6149659863945578, "std_accuracy": 0.010181380644282857, "total_folds": 3}, "0.419_0.581": {"average_accuracy": 0.5972789115646258, "std_accuracy": 0.032368373478558664, "total_folds": 3}, "0.269_0.731": {"average_accuracy": 0.5931972789115646, "std_accuracy": 0.029116917767391445, "total_folds": 3}, "0.445_0.555": {"average_accuracy": 0.5741496598639456, "std_accuracy": 0.001924100084861377, "total_folds": 3}, "0.228_0.772": {"average_accuracy": 0.6068027210884354, "std_accuracy": 0.020088194639773304, "total_folds": 3}, "0.894_0.106": {"average_accuracy": 0.5850340136054422, "std_accuracy": 0.03686045492284093, "total_folds": 3}, "0.100_0.900": {"average_accuracy": 0.6027210884353741, "std_accuracy": 0.02693740118805891, "total_folds": 3}, "0.900_0.100": {"average_accuracy": 0.6081632653061225, "std_accuracy": 0.03382259291062875, "total_folds": 3}, "0.113_0.887": {"average_accuracy": 0.580952380952381, "std_accuracy": 0.020088194639773304, "total_folds": 3}, "0.817_0.183": {"average_accuracy": 0.636734693877551, "std_accuracy": 0.026661112846619674, "total_folds": 3}}, "recommendation": "GOOD - Moderate accuracy (0.642) with statistical significance"}, "Clay_Set2_Mid": {"optimal_balance": {"historical_ratio": 0.17997993265440235, "momentum_ratio": 0.8200200673455976, "accuracy": 0.6310202573643411, "confidence_interval_95": [0.6093023255813953, 0.6465116279069767], "is_statistically_significant": true, "p_value": 0.0, "sample_size": 129}, "all_ratios_tested": {"0.737_0.263": {"average_accuracy": 0.6263565891472868, "std_accuracy": 0.0389761398138807, "total_folds": 3}, "0.247_0.753": {"average_accuracy": 0.6155038759689923, "std_accuracy": 0.023204076817202757, "total_folds": 3}, "0.724_0.276": {"average_accuracy": 0.5813953488372092, "std_accuracy": 0.0037976585159429457, "total_folds": 3}, "0.577_0.423": {"average_accuracy": 0.5984496124031008, "std_accuracy": 0.007905456610221363, "total_folds": 3}, "0.457_0.543": {"average_accuracy": 0.5937984496124031, "std_accuracy": 0.030696108330578807, "total_folds": 3}, "0.180_0.820": {"average_accuracy": 0.6310077519379845, "std_accuracy": 0.01581091322044276, "total_folds": 3}, "0.467_0.533": {"average_accuracy": 0.5829457364341085, "std_accuracy": 0.03274218927433003, "total_folds": 3}, "0.367_0.633": {"average_accuracy": 0.6093023255813953, "std_accuracy": 0.007595317031885845, "total_folds": 3}, "0.214_0.786": {"average_accuracy": 0.5736434108527132, "std_accuracy": 0.01911446202470999, "total_folds": 3}, "0.621_0.379": {"average_accuracy": 0.6186046511627907, "std_accuracy": 0.03854202447955372, "total_folds": 3}, "0.900_0.100": {"average_accuracy": 0.5658914728682171, "std_accuracy": 0.009557231012355028, "total_folds": 3}, "0.175_0.825": {"average_accuracy": 0.5968992248062016, "std_accuracy": 0.012207764145754762, "total_folds": 3}, "0.187_0.813": {"average_accuracy": 0.6155038759689923, "std_accuracy": 0.009557231012355021, "total_folds": 3}, "0.745_0.255": {"average_accuracy": 0.5984496124031008, "std_accuracy": 0.032742189274329994, "total_folds": 3}, "0.630_0.370": {"average_accuracy": 0.593798449612403, "std_accuracy": 0.008770316665879659, "total_folds": 3}, "0.193_0.807": {"average_accuracy": 0.6108527131782946, "std_accuracy": 0.021925791664699136, "total_folds": 3}, "0.611_0.389": {"average_accuracy": 0.5922480620155038, "std_accuracy": 0.024415528291509555, "total_folds": 3}, "0.257_0.743": {"average_accuracy": 0.6201550387596899, "std_accuracy": 0.010962895832349594, "total_folds": 3}, "0.100_0.900": {"average_accuracy": 0.586046511627907, "std_accuracy": 0.0, "total_folds": 3}, "0.318_0.682": {"average_accuracy": 0.5720930232558139, "std_accuracy": 0.027385305011869247, "total_folds": 3}, "0.265_0.735": {"average_accuracy": 0.5968992248062016, "std_accuracy": 0.015348054165289449, "total_folds": 3}, "0.238_0.762": {"average_accuracy": 0.6186046511627907, "std_accuracy": 0.01740305761290208, "total_folds": 3}, "0.251_0.749": {"average_accuracy": 0.5937984496124031, "std_accuracy": 0.036884890708125, "total_folds": 3}}, "recommendation": "GOOD - Moderate accuracy (0.631) with statistical significance"}}, "overall_summary": {"overall_accuracy": 0.5999798038958285, "contexts_tested": 2, "statistically_significant_contexts": 2, "system_reliability_score": 1.0, "optimal_ratios_by_context": {"Clay_Set1_Mid": {"historical_ratio": 0.456666202282873, "momentum_ratio": 0.543333797717127, "accuracy": 0.6421901183673473}, "Clay_Set2_Mid": {"historical_ratio": 0.17997993265440235, "momentum_ratio": 0.8200200673455976, "accuracy": 0.6310202573643411}}}, "recommendations": ["GOOD: Balance ratio optimization shows good reliability across contexts.", "IMPLEMENTATION: Apply context-specific optimal ratios:", "  • Clay_Set1_Mid: 0.5/0.5 (accuracy: 0.642)", "  • Clay_Set2_Mid: 0.2/0.8 (accuracy: 0.631)"]}