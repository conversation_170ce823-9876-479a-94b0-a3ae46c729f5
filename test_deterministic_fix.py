#!/usr/bin/env python3
"""
Test the deterministic simulation fix
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from enhanced_adaptive_learning_system import EnhancedAdaptiveLearningSystem, RobustBalanceValidationConfig

def test_deterministic_fix():
    """Test that deterministic mode produces realistic results"""
    
    print("🧪 TESTING DETERMINISTIC SIMULATION FIX")
    print("=" * 50)
    
    # Test with deterministic mode
    config = RobustBalanceValidationConfig(
        deterministic_mode=True,
        simulation_runs_per_ratio=1
    )
    
    enhanced_system = EnhancedAdaptiveLearningSystem()
    enhanced_system.robust_balance_validator.config = config
    
    # Get some test predictions
    learning_eligible = enhanced_system.get_learning_eligible_predictions()
    if not learning_eligible:
        print("❌ No learning eligible predictions found!")
        return
    
    test_predictions = learning_eligible[:10]  # Test with 10 predictions
    validator = enhanced_system.robust_balance_validator
    
    print(f"📊 Testing with {len(test_predictions)} predictions")
    
    # Test different ratios
    test_ratios = [(0.3, 0.7), (0.5, 0.5), (0.7, 0.3)]
    
    for hist_ratio, mom_ratio in test_ratios:
        print(f"\n🔍 Testing ratio {hist_ratio:.1f}/{mom_ratio:.1f}:")
        
        results = []
        for pred in test_predictions:
            # Test deterministic consistency
            result1 = validator._simulate_prediction_with_balance(pred, hist_ratio, mom_ratio)
            result2 = validator._simulate_prediction_with_balance(pred, hist_ratio, mom_ratio)
            result3 = validator._simulate_prediction_with_balance(pred, hist_ratio, mom_ratio)
            
            is_consistent = (result1 == result2 == result3)
            results.append({
                'prediction_id': pred.prediction_id,
                'result': result1,
                'consistent': is_consistent,
                'actual_correct': pred.was_correct
            })
        
        # Analyze results
        consistent_count = sum(1 for r in results if r['consistent'])
        sim_accuracy = sum(1 for r in results if r['result']) / len(results)
        actual_accuracy = sum(1 for r in results if r['actual_correct']) / len(results)
        
        print(f"   Consistency: {consistent_count}/{len(results)} (should be {len(results)})")
        print(f"   Simulated accuracy: {sim_accuracy:.3f}")
        print(f"   Actual accuracy: {actual_accuracy:.3f}")
        print(f"   Realistic range: {0.4 <= sim_accuracy <= 0.8}")
        
        # Check for issues
        if consistent_count != len(results):
            print("   ❌ ISSUE: Deterministic mode not consistent!")
        elif sim_accuracy == 1.0 or sim_accuracy == 0.0:
            print("   ❌ ISSUE: Unrealistic perfect accuracy!")
        elif not (0.4 <= sim_accuracy <= 0.8):
            print("   ⚠️ WARNING: Accuracy outside realistic range")
        else:
            print("   ✅ Results look good!")

def test_non_deterministic():
    """Test that non-deterministic mode produces varied results"""
    
    print("\n🎲 TESTING NON-DETERMINISTIC MODE")
    print("=" * 50)
    
    # Test with non-deterministic mode
    config = RobustBalanceValidationConfig(
        deterministic_mode=False,
        simulation_runs_per_ratio=1
    )
    
    enhanced_system = EnhancedAdaptiveLearningSystem()
    enhanced_system.robust_balance_validator.config = config
    
    learning_eligible = enhanced_system.get_learning_eligible_predictions()
    test_predictions = learning_eligible[:5]  # Smaller sample for variability test
    validator = enhanced_system.robust_balance_validator
    
    print(f"📊 Testing with {len(test_predictions)} predictions")
    
    # Test one ratio multiple times
    hist_ratio, mom_ratio = 0.5, 0.5
    print(f"\n🔍 Testing ratio {hist_ratio:.1f}/{mom_ratio:.1f} multiple times:")
    
    for pred in test_predictions:
        results = []
        for run in range(10):
            result = validator._simulate_prediction_with_balance(pred, hist_ratio, mom_ratio)
            results.append(result)
        
        unique_results = len(set(results))
        true_count = sum(results)
        
        print(f"   Pred {pred.prediction_id}: {true_count}/10 True, {unique_results} unique results")
        
        if unique_results == 1:
            print("   ⚠️ WARNING: No variability in non-deterministic mode")

def main():
    """Main test function"""
    
    try:
        test_deterministic_fix()
        test_non_deterministic()
        
        print("\n📋 SUMMARY:")
        print("=" * 50)
        print("✅ If deterministic mode shows:")
        print("   • 100% consistency")
        print("   • Realistic accuracy (40-80%)")
        print("   • No perfect 0% or 100% accuracy")
        print("   → The fix is working!")
        print()
        print("✅ If non-deterministic mode shows:")
        print("   • Some variability in results")
        print("   • Realistic accuracy ranges")
        print("   → Normal randomness is working!")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
