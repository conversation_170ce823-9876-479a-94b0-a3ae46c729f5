#!/usr/bin/env python3
"""
Test script to verify the improved cross-validation fold reporting
"""

from enhanced_adaptive_learning_system import EnhancedAdaptiveLearningSystem
import j<PERSON>

def test_improved_reporting():
    """Test the improved fold reporting functionality"""
    print("🧪 Testing Improved Cross-Validation Fold Reporting")
    print("=" * 60)
    
    # Create system
    system = EnhancedAdaptiveLearningSystem()
    
    # Lower requirements to ensure we get some results
    original_min_context = system.robust_balance_validator.config.min_context_sample_size
    original_min_train = system.robust_balance_validator.config.min_train_size
    original_min_test = system.robust_balance_validator.config.min_test_size
    
    system.robust_balance_validator.config.min_context_sample_size = 100
    system.robust_balance_validator.config.min_train_size = 50
    system.robust_balance_validator.config.min_test_size = 15
    
    print(f"📊 Lowered requirements: context={100}, train={50}, test={15}")
    
    # Get predictions
    learning_predictions = system.get_learning_eligible_predictions()
    print(f"📈 Found {len(learning_predictions)} learning-eligible predictions")
    
    if len(learning_predictions) < 100:
        print("❌ Not enough predictions for testing")
        return
    
    # Test with a subset to make it faster
    test_predictions = learning_predictions[:200]
    print(f"🎯 Testing with {len(test_predictions)} predictions")
    
    try:
        # Run validation
        result = system.robust_balance_validator.validate_balance_ratios(test_predictions)
        
        print(f"\n📋 VALIDATION RESULTS:")
        print(f"   Status: {result.get('status', 'unknown')}")
        
        # Check context results
        context_results = result.get('context_results', {})
        print(f"   Contexts found: {len(context_results)}")
        
        for context_key, context_data in context_results.items():
            print(f"\n🔍 Context: {context_key}")
            
            # Check for improved reporting
            cv_info = context_data.get('cross_validation_info', {})
            if cv_info:
                print(f"   ✅ Cross-validation info found:")
                print(f"      Configured folds: {cv_info.get('configured_folds', 'N/A')}")
                print(f"      Valid folds: {cv_info.get('valid_folds', 'N/A')}")
                print(f"      Skipped folds: {cv_info.get('skipped_folds', 'N/A')}")
                print(f"      Usage rate: {cv_info.get('fold_usage_rate', 0):.1%}")
            else:
                print(f"   ❌ No cross_validation_info found")
            
            # Check sample ratio data
            all_ratios = context_data.get('all_ratios_tested', {})
            if all_ratios:
                sample_ratio = next(iter(all_ratios.values()))
                print(f"   📊 Sample ratio data:")
                print(f"      total_folds: {sample_ratio.get('total_folds', 'N/A')}")
                print(f"      configured_folds: {sample_ratio.get('configured_folds', 'N/A')}")
                print(f"      valid_folds: {sample_ratio.get('valid_folds', 'N/A')}")
                print(f"      skipped_folds: {sample_ratio.get('skipped_folds', 'N/A')}")
        
        # Check overall summary
        overall_summary = result.get('overall_summary', {})
        cv_summary = overall_summary.get('cross_validation_summary', {})
        
        if cv_summary:
            print(f"\n✅ OVERALL CROSS-VALIDATION SUMMARY:")
            print(f"   Total configured folds: {cv_summary.get('total_configured_folds', 'N/A')}")
            print(f"   Total valid folds: {cv_summary.get('total_valid_folds', 'N/A')}")
            print(f"   Total skipped folds: {cv_summary.get('total_skipped_folds', 'N/A')}")
            print(f"   Overall usage rate: {cv_summary.get('overall_fold_usage_rate', 0):.1%}")
        else:
            print(f"\n❌ No cross_validation_summary in overall_summary")
        
        # Check recommendations for warnings
        recommendations = result.get('recommendations', [])
        warning_recs = [r for r in recommendations if '⚠️' in r]
        
        print(f"\n📝 RECOMMENDATIONS ({len(recommendations)} total):")
        for i, rec in enumerate(recommendations, 1):
            marker = "⚠️" if '⚠️' in rec else "📌"
            print(f"   {marker} {rec}")
        
        if warning_recs:
            print(f"\n✅ Found {len(warning_recs)} warning recommendations!")
        else:
            print(f"\n❌ No warning recommendations found")
            
        print(f"\n🎉 Test completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during validation: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # Restore original settings
        system.robust_balance_validator.config.min_context_sample_size = original_min_context
        system.robust_balance_validator.config.min_train_size = original_min_train
        system.robust_balance_validator.config.min_test_size = original_min_test

if __name__ == "__main__":
    test_improved_reporting()
