#!/usr/bin/env python3
"""
Test the improved fold reporting directly
"""

from enhanced_adaptive_learning_system import EnhancedAdaptiveLearningSystem
from sklearn.model_selection import TimeSeriesSplit
import json

def test_fold_reporting():
    """Test the improved fold reporting functionality directly"""
    print("🧪 Testing Fold Reporting Logic")
    print("=" * 50)
    
    # Create system
    system = EnhancedAdaptiveLearningSystem()
    
    # Get predictions
    learning_predictions = system.get_learning_eligible_predictions()
    print(f"📈 Found {len(learning_predictions)} learning-eligible predictions")
    
    # Segment predictions
    segmented_data = system.robust_balance_validator._segment_predictions_by_context(learning_predictions)
    
    print(f"\n📊 Context Segmentation:")
    for context_key, predictions in segmented_data.items():
        print(f"   {context_key}: {len(predictions)} predictions")
    
    # Test with Clay_Set1_Mid if it has enough data
    test_context = "Clay_Set1_Mid"
    if test_context in segmented_data and len(segmented_data[test_context]) >= 150:
        predictions = segmented_data[test_context]
        print(f"\n🎯 Testing with {test_context}: {len(predictions)} predictions")
        
        # Sort by timestamp for temporal validation
        predictions.sort(key=lambda x: x.timestamp)
        
        # Create time series splits
        tscv = TimeSeriesSplit(n_splits=5)
        splits = list(tscv.split(predictions))
        
        print(f"\n📋 TimeSeriesSplit Analysis:")
        print(f"   Configured splits: {len(splits)}")
        
        valid_folds = 0
        for i, (train_indices, test_indices) in enumerate(splits):
            train_size = len(train_indices)
            test_size = len(test_indices)
            is_valid = (train_size >= system.robust_balance_validator.config.min_train_size and 
                       test_size >= system.robust_balance_validator.config.min_test_size)
            
            status = "✅ VALID" if is_valid else "❌ SKIPPED"
            if is_valid:
                valid_folds += 1
                
            print(f"   Fold {i+1}: train={train_size}, test={test_size} - {status}")
            
            if not is_valid:
                reasons = []
                if train_size < system.robust_balance_validator.config.min_train_size:
                    reasons.append(f"train < {system.robust_balance_validator.config.min_train_size}")
                if test_size < system.robust_balance_validator.config.min_test_size:
                    reasons.append(f"test < {system.robust_balance_validator.config.min_test_size}")
                print(f"      Reason: {', '.join(reasons)}")
        
        print(f"\n📊 Summary:")
        print(f"   Configured folds: {len(splits)}")
        print(f"   Valid folds: {valid_folds}")
        print(f"   Skipped folds: {len(splits) - valid_folds}")
        print(f"   Usage rate: {valid_folds / len(splits):.1%}")
        
        # Test the _evaluate_ratio_grid method directly
        print(f"\n🔧 Testing _evaluate_ratio_grid method:")
        
        # Use a small set of ratios for testing
        test_ratios = [(0.5, 0.5), (0.6, 0.4)]
        
        try:
            ratio_results = system.robust_balance_validator._evaluate_ratio_grid(
                test_context, predictions, splits, test_ratios
            )
            
            print(f"   Results returned: {len(ratio_results)} ratios")
            
            for ratio_key, ratio_data in ratio_results.items():
                print(f"\n   📈 Ratio {ratio_key}:")
                print(f"      total_folds: {ratio_data.get('total_folds', 'N/A')}")
                print(f"      configured_folds: {ratio_data.get('configured_folds', 'N/A')}")
                print(f"      valid_folds: {ratio_data.get('valid_folds', 'N/A')}")
                print(f"      skipped_folds: {ratio_data.get('skipped_folds', 'N/A')}")
                
                # Check if our new fields are present
                has_new_fields = all(key in ratio_data for key in ['configured_folds', 'valid_folds', 'skipped_folds'])
                print(f"      ✅ New fields present: {has_new_fields}")
                
        except Exception as e:
            print(f"   ❌ Error testing _evaluate_ratio_grid: {e}")
            import traceback
            traceback.print_exc()
            
    else:
        print(f"\n❌ {test_context} not available or insufficient data")
        print("Available contexts:")
        for context_key, predictions in segmented_data.items():
            status = "✅" if len(predictions) >= 150 else "❌"
            print(f"   {status} {context_key}: {len(predictions)} predictions")

if __name__ == "__main__":
    test_fold_reporting()
