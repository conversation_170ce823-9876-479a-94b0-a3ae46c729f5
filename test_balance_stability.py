"""
Test script to verify balance ratio stability improvements
"""

import json
from datetime import datetime
from enhanced_adaptive_learning_system import (
    EnhancedAdaptiveLearningSystem, 
    RobustBalanceValidationConfig,
    force_balance_validation
)

def test_balance_stability():
    """Test balance ratio consistency across multiple runs"""
    print("🧪 Testing Balance Ratio Stability...")
    print("=" * 60)
    
    # Run balance validation multiple times to check consistency
    results = []
    
    for run_idx in range(3):
        print(f"\n🔄 Run {run_idx + 1}/3")
        print("-" * 30)
        
        try:
            result = force_balance_validation()
            
            if result.get('status') == 'success':
                # Extract optimal ratios for each context
                context_results = result.get('context_results', {})
                run_ratios = {}
                
                for context_key, context_data in context_results.items():
                    optimal_balance = context_data.get('optimal_balance', {})
                    if optimal_balance.get('is_statistically_significant', False):
                        hist_ratio = optimal_balance.get('historical_ratio', 0.5)
                        mom_ratio = optimal_balance.get('momentum_ratio', 0.5)
                        accuracy = optimal_balance.get('accuracy', 0.0)
                        
                        run_ratios[context_key] = {
                            'historical_ratio': hist_ratio,
                            'momentum_ratio': mom_ratio,
                            'accuracy': accuracy
                        }
                        
                        print(f"   {context_key}: {hist_ratio:.3f}/{mom_ratio:.3f} (acc: {accuracy:.3f})")
                
                results.append({
                    'run': run_idx + 1,
                    'ratios': run_ratios,
                    'timestamp': datetime.now().isoformat()
                })
            else:
                print(f"   ❌ Run failed: {result.get('status')}")
                results.append({
                    'run': run_idx + 1,
                    'ratios': {},
                    'error': result.get('status'),
                    'timestamp': datetime.now().isoformat()
                })
                
        except Exception as e:
            print(f"   ❌ Run error: {e}")
            results.append({
                'run': run_idx + 1,
                'ratios': {},
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            })
    
    # Analyze consistency
    print("\n📊 CONSISTENCY ANALYSIS")
    print("=" * 60)
    
    analyze_consistency(results)
    
    # Save results
    save_stability_test_results(results)
    
    return results

def analyze_consistency(results):
    """Analyze consistency across runs"""
    
    # Get all contexts that appeared in successful runs
    all_contexts = set()
    successful_runs = [r for r in results if r['ratios']]
    
    for result in successful_runs:
        all_contexts.update(result['ratios'].keys())
    
    if not all_contexts:
        print("❌ No successful runs to analyze")
        return
    
    print(f"📈 Analyzing {len(all_contexts)} contexts across {len(successful_runs)} successful runs")
    
    for context in sorted(all_contexts):
        print(f"\n🎯 Context: {context}")
        
        # Collect ratios for this context across runs
        context_ratios = []
        context_accuracies = []
        
        for result in successful_runs:
            if context in result['ratios']:
                ratio_data = result['ratios'][context]
                hist_ratio = ratio_data['historical_ratio']
                accuracy = ratio_data['accuracy']
                
                context_ratios.append(hist_ratio)
                context_accuracies.append(accuracy)
                
                print(f"   Run {result['run']}: {hist_ratio:.3f} (acc: {accuracy:.3f})")
        
        if len(context_ratios) >= 2:
            # Calculate variance
            import numpy as np
            ratio_std = np.std(context_ratios)
            accuracy_std = np.std(context_accuracies)
            ratio_range = max(context_ratios) - min(context_ratios)
            
            print(f"   📊 Ratio std dev: {ratio_std:.4f}")
            print(f"   📊 Ratio range: {ratio_range:.4f}")
            print(f"   📊 Accuracy std dev: {accuracy_std:.4f}")
            
            # Consistency assessment
            if ratio_std <= 0.02:  # Within 2% standard deviation
                print("   ✅ CONSISTENT ratios")
            elif ratio_std <= 0.05:  # Within 5% standard deviation
                print("   ⚠️ MODERATELY consistent ratios")
            else:
                print("   ❌ INCONSISTENT ratios")
        else:
            print("   ⚠️ Insufficient data for consistency analysis")

def save_stability_test_results(results):
    """Save test results to file"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"balance_stability_test_{timestamp}.json"
    
    test_summary = {
        'test_timestamp': datetime.now().isoformat(),
        'test_type': 'balance_ratio_stability',
        'total_runs': len(results),
        'successful_runs': len([r for r in results if r['ratios']]),
        'results': results
    }
    
    try:
        with open(filename, 'w') as f:
            json.dump(test_summary, f, indent=2)
        print(f"\n💾 Results saved to: {filename}")
    except Exception as e:
        print(f"\n❌ Failed to save results: {e}")

if __name__ == "__main__":
    test_balance_stability()
